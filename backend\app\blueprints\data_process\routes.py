# app/blueprints/api/routes.py
import os
import time

from flask import (abort, current_app, jsonify, redirect, request, send_file,
                   send_from_directory, url_for)

from ...services.file_processor import (process_file,
                                        read_file)
from ...services.json_session_service import (get_session_value,
                                              set_session_value)
from ...services.nlp_service import ( get_language_groups,
                                     get_pos_tags, get_spacy_status)
from ...services.session_service import (get_or_create_session_id,
                                         get_session_by_id, require_session)
from . import process as step2


# Configure CORS for React frontend with HTTPS support
@step2.after_request
def after_request(response):
    # Get CORS allowed origins from environment variable or use a default
    cors_allowed_origins = os.getenv("CORS_ALLOWED_ORIGINS", "*")

    # If specific origins are provided, use them
    if cors_allowed_origins != "*":
        # Check if the request origin is in the allowed origins
        request_origin = request.headers.get("Origin")
        if request_origin:
            if "," in cors_allowed_origins:
                allowed_origins = cors_allowed_origins.split(",")
                if request_origin in allowed_origins:
                    response.headers.add("Access-Control-Allow-Origin", request_origin)
            else:
                if request_origin == cors_allowed_origins:
                    response.headers.add("Access-Control-Allow-Origin", request_origin)
    else:
        # Wildcard for development
        response.headers.add("Access-Control-Allow-Origin", "*")

    response.headers.add(
        "Access-Control-Allow-Headers", "Content-Type,Authorization,X-Requested-With"
    )
    response.headers.add("Access-Control-Allow-Methods", "GET,PUT,POST,DELETE,OPTIONS")
    response.headers.add("Access-Control-Allow-Credentials", "true")

    # Add Vary header to ensure proper caching with CORS
    response.headers.add("Vary", "Origin")

    # Set UTF-8 charset for JSON responses to properly handle Korean characters
    if response.content_type and response.content_type.startswith('application/json'):
        response.content_type = 'application/json; charset=utf-8'

    return response


@step2.route("/language-groups", methods=["GET"])
def language_groups():
    """Get available language groups and analyzers"""
    return jsonify(get_language_groups())


@step2.route("/pos-tags/<language>/<analyzer>", methods=["GET"])
def pos_tags(language, analyzer):
    """Get POS tags for the selected language and analyzer"""
    return jsonify(get_pos_tags(language, analyzer))


@step2.route("/spacy-status", methods=["GET"])
def spacy_status():
    """Check if spaCy model is installed"""
    return jsonify(get_spacy_status())


@step2.route("/columns", methods=["GET"])
@require_session
def columns():
    """Get columns from an uploaded Excel file"""
    # Check if a specific session ID was provided in the query parameters
    query_session_id = request.args.get("session_id")

    # Get or create session ID from the session or use the provided one
    if query_session_id:
        # Verify that the session exists
        session_data = get_session_by_id(query_session_id)
        if session_data:
            session_id = query_session_id
        else:
            return (
                jsonify({"error": f"Session with ID {query_session_id} not found"}),
                404,
            )
    else:
        # Get or create session ID
        session_id = get_or_create_session_id()

    # Check if a file has been uploaded
    filename = get_session_value(session_id, "uploaded_file")
    if not filename:
        return jsonify({"error": "파일이 업로드되지 않았습니다."}), 400

    try:
        # Get session folder
        upload_folder = current_app.config["UPLOAD_FOLDER"]
        session_folder = os.path.join(upload_folder, session_id)

        # print(current_app.config['UPLOAD_FOLDER'])
        filepath = os.path.join(session_folder, filename)

        if not os.path.exists(filepath):
            return (
                jsonify({"success": False, "error": "파일을 찾을 수 없습니다. Error"}),
                404,
            )
        data = read_file(filepath)

        columns = data.columns.to_list()
        print(columns)
        print(data)
        # Delete temporary file
        # if os.path.exists(filepath):
        #     os.unlink(filepath)
        # os.unlink(filename)

        return jsonify({"success": True, "columns": columns})
    except Exception as e:
        # Try to delete temporary file on error
        try:
            if os.path.exists(filepath):
                os.unlink(filepath)
        except:
            pass
        return jsonify({"error": str(e)}), 500


@step2.route("/process", methods=["POST"])
@require_session
def process():
    """Process an Excel file with NLP analysis and return results immediately"""
    # Check if a specific session ID was provided in the query parameters
    query_session_id = get_or_create_session_id()

    # Get or create session ID from the session or use the provided one
    if query_session_id:
        # Verify that the session exists
        session_data = get_session_by_id(query_session_id)
        if session_data:
            session_id = query_session_id
        else:
            return (
                jsonify({"error": f"Session with ID {query_session_id} not found"}),
                404,
            )
    else:
        # Get or create session ID
        session_id = get_or_create_session_id()

    # Check if a file has been uploaded
    filename = get_session_value(session_id, "uploaded_file")
    if not filename:
        # ideal redirect back to upload
        return jsonify({"error": "No file uploaded"}), 400

    column_name = request.form.get("column_name", "")

    if not column_name:
        return jsonify({"error": "Column name not specified"}), 400

    # Check language and analyzer options
    language = request.form.get("language", "korean")
    analyzer = request.form.get("analyzer", "okt")

    # Check if spaCy is available for English
    spacy_status = get_spacy_status()
    if language.lower() == "english" and not spacy_status.get("installed", False):
        return (
            jsonify(
                {
                    "error": "spaCy English model is not installed. Please contact server administrator."
                }
            ),
            400,
        )

    # Collect options
    options = {
        "language": language,
        "analyzer": analyzer,
        "pos_tags": request.form.getlist("pos_tags"),
        "min_word_length": request.form.get("min_word_length", "2"),
        "custom_filename": request.form.get("custom_filename", ""),
        "original_filename": os.path.splitext(filename)[0],
        "column_name": column_name,
    }
    # Store options in session
    set_session_value(session_id, "options", options)

    # Create result folder if it doesn't exist (changed from PROCESSED_FOLDER to RESULT_FOLDER)
    RESULT_FOLDER = current_app.config["RESULT_FOLDER"]
    if not os.path.exists(RESULT_FOLDER):
        os.makedirs(RESULT_FOLDER)

    # Get file path from session
    upload_folder = current_app.config["UPLOAD_FOLDER"]
    session_folder = os.path.join(upload_folder, session_id)
    filepath = os.path.join(session_folder, filename)

    # Process file directly (no background task)
    try:
        # Process the file immediately, passing the session_id
        output_file, error = process_file(
            filepath, column_name, options=options, session_id=session_id
        )

        if error:
            return jsonify({"error": error}), 500

        # Build output filename
        pos_tags = options.get("pos_tags", [])
        if len(pos_tags) == 0:
            pos_tags_str = ""
        elif len(options["pos_tags"]) > 3:
            selected_tags = options["pos_tags"][:3] + ["etc"]
            pos_tags_str = "_".join(selected_tags)
        else:
            pos_tags_str = "_".join(pos_tags)

        output_filename = f"{options['original_filename']}_{options['column_name']}_{options['analyzer']}_{pos_tags_str}_정제_ver1.xlsx"

        # Check if output file exists in the processed_files subdirectory
        result_folder = current_app.config["RESULT_FOLDER"]
        session_folder = os.path.join(result_folder, session_id)
        processed_folder = os.path.join(session_folder, current_app.config["PROCESSED_FILES_FOLDER"])
        file_path = os.path.join(processed_folder, output_filename)

        if not os.path.isfile(file_path) and output_file:
            # If the file path from process_file doesn't match our expected path
            # but process_file succeeded, use the returned path
            output_filename = os.path.basename(output_file)
            file_path = output_file

        if not os.path.isfile(file_path):
            return jsonify({"error": "Output file not found after processing"}), 500

        # Return the result with download URL
        return jsonify(
            {
                "success": True,
                "session_id": session_id,
                "status": "completed",
                "download_url": url_for(
                    "process.serve_file",
                    filepath=f"{current_app.config['PROCESSED_FILES_FOLDER']}/{output_filename}",
                    download="true",
                    session_id=session_id,
                ),
                "filename": output_filename,
            }
        )

    except Exception as e:
        # Try to delete temporary file on error
        try:
            if os.path.exists(filepath):
                os.unlink(filepath)
        except:
            pass
        return jsonify({"error": str(e)}), 500


@step2.route("/progress/<task_id>", methods=["GET"])
@require_session
def progress(task_id):
    """Get progress of a processing task"""
    # Check if a specific session ID was provided in the query parameters
    query_session_id = request.args.get("session_id")

    # Get or create session ID from the session or use the provided one
    if query_session_id:
        # Verify that the session exists
        session_data = get_session_by_id(query_session_id)
        if not session_data:
            return (
                jsonify({"error": f"Session with ID {query_session_id} not found"}),
                404,
            )
        session_id = query_session_id
    else:
        # Get or create session ID
        session_id = get_or_create_session_id()

    # Since processing is now synchronous, always return 100% progress
    # This endpoint is kept for backward compatibility
    return jsonify(
        {
            "task_id": task_id,
            "progress": 100,  # Always complete since processing is now synchronous
            "session_id": session_id,
        }
    )


@step2.route("/result/<task_id>", methods=["GET"])
@require_session
def get_result(task_id):
    """Get the result of a completed task"""
    # Check if a specific session ID was provided in the query parameters
    query_session_id = request.args.get("session_id")

    # Get or create session ID from the session or use the provided one
    if query_session_id:
        # Verify that the session exists
        session_data = get_session_by_id(query_session_id)
        if session_data:
            session_id = query_session_id
        else:
            return (
                jsonify({"error": f"Session with ID {query_session_id} not found"}),
                404,
            )
    else:
        # Get or create session ID
        session_id = get_or_create_session_id()

    # Get uploaded file information
    uploaded_file = get_session_value(session_id, "uploaded_file")
    if not uploaded_file:
        return jsonify({"error": "No uploaded file found in session"}), 404

    # Get options
    options = get_session_value(session_id, "options")
    if not options:
        return jsonify({"error": "No processing options found in session"}), 404

    # Build output filename
    filename = uploaded_file.split(".")[0]
    pos_tags = options.get("pos_tags", [])
    if len(pos_tags) == 0:
        pos_tags_str = ""
    elif len(options["pos_tags"]) > 3:
        selected_tags = options["pos_tags"][:3] + ["etc"]
        pos_tags_str = "_".join(selected_tags)
    else:
        pos_tags_str = "_".join(pos_tags)

    output_file = f"{filename}_{options['column_name']}_{options['analyzer']}_{pos_tags_str}_정제_ver1.xlsx"

    # Check if output file exists in the processed_files subdirectory
    result_folder = current_app.config["RESULT_FOLDER"]
    session_folder = os.path.join(result_folder, session_id)
    processed_folder = os.path.join(session_folder, current_app.config["PROCESSED_FILES_FOLDER"])
    file_path = os.path.join(processed_folder, output_file)

    if not os.path.isfile(file_path):
        return jsonify({"error": "Output file not found"}), 404

    # Return the result with download URL
    return jsonify(
        {
            "success": True,
            "task_id": task_id,
            "status": "completed",
            "download_url": url_for(
                "process.serve_file",
                filepath=f"{current_app.config['PROCESSED_FILES_FOLDER']}/{os.path.basename(output_file)}",
                download="true",
                session_id=session_id,
            ),
            "filename": os.path.basename(output_file),
        }
    )


@step2.route("/files/<path:filepath>", methods=["GET"])
@require_session
def serve_file(filepath):
    """
    Unified endpoint to serve or download files from the results folder.

    Args:
        filepath: Path to the file relative to the session folder

    Query Parameters:
        download: Set to 'true' to download the file as an attachment
        session_id: Optional session ID to override the current session
    """
    result_folder = current_app.config["RESULT_FOLDER"]

    # Check if a specific session ID was provided in the query parameters
    query_session_id = request.args.get("session_id")

    # Get or create session ID from the session or use the provided one
    if query_session_id:
        # Verify that the session exists
        session_data = get_session_by_id(query_session_id)
        if session_data:
            session_id = query_session_id
        else:
            return (
                jsonify({"error": f"Session with ID {query_session_id} not found"}),
                404,
            )
    else:
        # Get or create session ID
        session_id = get_or_create_session_id()

    # Ensure we have a valid session ID
    if not session_id:
        return jsonify({"error": "No valid session ID found"}), 400

    session_folder = os.path.join(result_folder, session_id)
    # analysis_category = request.args.get("analysis_category", None)
    # analysis_type = request.args.get("analysis_type", None)

    # if analysis_type:
    #     assert ana
    # if analysis_category:
    #     file_folder = os.path.join(session_folder, analysis_category)
    # else

    # Check if file exists
    file_path = os.path.join(session_folder, filepath)
    print(f"Looking for file at: {file_path}")
    print(f"Session folder: {session_folder}")
    print(f"Requested filepath: {filepath}")
    print(f"Session ID: {session_id}")

    if not os.path.isfile(file_path):
        print("File not found at primary location:", file_path)

        # List contents of session folder for debugging
        if os.path.exists(session_folder):
            print(f"Session folder contents: {os.listdir(session_folder)}")
            # Also check subdirectories
            for item in os.listdir(session_folder):
                item_path = os.path.join(session_folder, item)
                if os.path.isdir(item_path):
                    print(f"Subdirectory {item} contents: {os.listdir(item_path)}")
        else:
            print(f"Session folder does not exist: {session_folder}")

        # Try to find the file in the common folder (without session)
        common_path = os.path.join(result_folder, filepath)
        print(f"Trying common path: {common_path}")
        if os.path.isfile(common_path):
            print("Found file in common folder")
            # If found in common folder, serve from there
            return send_from_directory(
                result_folder,
                filepath,
                as_attachment=request.args.get("download", "false").lower() == "true",
            )

        return jsonify({"error": "File not found"}), 404

    # Determine if file should be downloaded as attachment
    download = request.args.get("download", "false").lower() == "true"

    # Serve the file
    return send_from_directory(session_folder, filepath, as_attachment=download)


# Backward compatibility routes
@step2.route("/download/<filename>", methods=["GET"])
@require_session
def download(filename):
    """Redirect to the unified file endpoint with download=true"""
    # Check if a specific session ID was provided in the query parameters
    query_session_id = request.args.get("session_id")

    # Redirect to the files endpoint with download=true
    if query_session_id:
        return redirect(
            url_for(
                "process.serve_file",
                filepath=filename,
                download="true",
                session_id=query_session_id,
            )
        )
    else:
        return redirect(
            url_for("process.serve_file", filepath=filename, download="true")
        )


@step2.route("/results/<path:subpath>")
@require_session
def serve_results_file(subpath):
    """Redirect to the unified file endpoint"""
    # Check if a specific session ID was provided in the query parameters
    query_session_id = request.args.get("session_id")

    # Redirect to the files endpoint
    if query_session_id:
        return redirect(
            url_for("process.serve_file", filepath=subpath, session_id=query_session_id)
        )
    else:
        return redirect(url_for("process.serve_file", filepath=subpath))
